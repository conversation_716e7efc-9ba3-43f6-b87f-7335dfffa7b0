#!/bin/bash

# SQL端口转发脚本
# 将本地端口转发到阿里云RDS MySQL数据库

# 数据库配置
DB_HOST="rm-2ze3im04rxy44o880.mysql.rds.aliyuncs.com"
DB_PORT="3306"
DB_USER="automatic_targeting_test"
DB_PASSWORD="kru6YVF7bze*wtj8aux"

# 本地转发端口（可以修改）
LOCAL_PORT="3307"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== SQL端口转发工具 ===${NC}"
echo -e "目标数据库: ${YELLOW}${DB_HOST}:${DB_PORT}${NC}"
echo -e "本地端口: ${YELLOW}${LOCAL_PORT}${NC}"
echo -e "数据库用户: ${YELLOW}${DB_USER}${NC}"
echo ""

# 检查端口是否已被占用
check_port() {
    if netstat -tuln 2>/dev/null | grep -q ":${LOCAL_PORT} "; then
        echo -e "${RED}错误: 端口 ${LOCAL_PORT} 已被占用${NC}"
        echo "请修改 LOCAL_PORT 变量或停止占用该端口的进程"
        exit 1
    fi
}

# 使用socat进行端口转发（如果可用）
start_socat_forward() {
    echo -e "${GREEN}使用 socat 启动端口转发...${NC}"
    socat TCP-LISTEN:${LOCAL_PORT},fork TCP:${DB_HOST}:${DB_PORT} &
    SOCAT_PID=$!
    echo "socat PID: $SOCAT_PID"
    echo $SOCAT_PID > /tmp/sql-forward.pid
}

# 使用nc进行端口转发
start_nc_forward() {
    echo -e "${GREEN}使用 nc 启动端口转发...${NC}"
    while true; do
        nc -l -p ${LOCAL_PORT} -c "nc ${DB_HOST} ${DB_PORT}"
        sleep 1
    done &
    NC_PID=$!
    echo "nc PID: $NC_PID"
    echo $NC_PID > /tmp/sql-forward.pid
}

# 启动端口转发
start_forward() {
    check_port
    
    echo -e "${YELLOW}正在启动端口转发...${NC}"
    
    # 优先使用socat，如果不可用则使用nc
    if command -v socat >/dev/null 2>&1; then
        start_socat_forward
    else
        echo -e "${YELLOW}socat 不可用，使用 nc 替代${NC}"
        start_nc_forward
    fi
    
    sleep 2
    
    # 验证端口转发是否成功
    if netstat -tuln 2>/dev/null | grep -q ":${LOCAL_PORT} "; then
        echo -e "${GREEN}✓ 端口转发启动成功！${NC}"
        echo -e "现在可以通过以下方式连接数据库："
        echo -e "  主机: ${YELLOW}localhost${NC} 或 ${YELLOW}127.0.0.1${NC}"
        echo -e "  端口: ${YELLOW}${LOCAL_PORT}${NC}"
        echo -e "  用户名: ${YELLOW}${DB_USER}${NC}"
        echo -e "  密码: ${YELLOW}${DB_PASSWORD}${NC}"
        echo ""
        echo -e "MySQL连接命令示例:"
        echo -e "${YELLOW}mysql -h 127.0.0.1 -P ${LOCAL_PORT} -u ${DB_USER} -p${NC}"
        echo ""
        echo -e "要停止端口转发，请运行: ${YELLOW}./sql-port-forward.sh stop${NC}"
    else
        echo -e "${RED}✗ 端口转发启动失败${NC}"
        exit 1
    fi
}

# 停止端口转发
stop_forward() {
    if [ -f /tmp/sql-forward.pid ]; then
        PID=$(cat /tmp/sql-forward.pid)
        if kill -0 $PID 2>/dev/null; then
            echo -e "${YELLOW}正在停止端口转发 (PID: $PID)...${NC}"
            kill $PID
            rm -f /tmp/sql-forward.pid
            echo -e "${GREEN}✓ 端口转发已停止${NC}"
        else
            echo -e "${YELLOW}端口转发进程已不存在${NC}"
            rm -f /tmp/sql-forward.pid
        fi
    else
        echo -e "${YELLOW}未找到运行中的端口转发${NC}"
    fi
}

# 检查状态
check_status() {
    if [ -f /tmp/sql-forward.pid ]; then
        PID=$(cat /tmp/sql-forward.pid)
        if kill -0 $PID 2>/dev/null; then
            echo -e "${GREEN}✓ 端口转发正在运行 (PID: $PID)${NC}"
            echo -e "本地端口: ${YELLOW}${LOCAL_PORT}${NC}"
            if netstat -tuln 2>/dev/null | grep -q ":${LOCAL_PORT} "; then
                echo -e "${GREEN}✓ 端口 ${LOCAL_PORT} 正在监听${NC}"
            else
                echo -e "${RED}✗ 端口 ${LOCAL_PORT} 未在监听${NC}"
            fi
        else
            echo -e "${RED}✗ 端口转发进程已停止${NC}"
            rm -f /tmp/sql-forward.pid
        fi
    else
        echo -e "${YELLOW}端口转发未运行${NC}"
    fi
}

# 测试连接
test_connection() {
    echo -e "${YELLOW}测试数据库连接...${NC}"
    
    # 测试原始连接
    echo -e "测试直连数据库..."
    if timeout 5 bash -c "</dev/tcp/${DB_HOST}/${DB_PORT}" 2>/dev/null; then
        echo -e "${GREEN}✓ 可以直接连接到数据库${NC}"
    else
        echo -e "${RED}✗ 无法直接连接到数据库${NC}"
    fi
    
    # 测试本地转发端口
    if netstat -tuln 2>/dev/null | grep -q ":${LOCAL_PORT} "; then
        echo -e "测试本地转发端口..."
        if timeout 5 bash -c "</dev/tcp/127.0.0.1/${LOCAL_PORT}" 2>/dev/null; then
            echo -e "${GREEN}✓ 本地转发端口可用${NC}"
        else
            echo -e "${RED}✗ 本地转发端口不可用${NC}"
        fi
    else
        echo -e "${YELLOW}本地转发端口未启动${NC}"
    fi
}

# 主逻辑
case "${1:-start}" in
    "start")
        start_forward
        ;;
    "stop")
        stop_forward
        ;;
    "restart")
        stop_forward
        sleep 2
        start_forward
        ;;
    "status")
        check_status
        ;;
    "test")
        test_connection
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|test}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动端口转发 (默认)"
        echo "  stop    - 停止端口转发"
        echo "  restart - 重启端口转发"
        echo "  status  - 查看转发状态"
        echo "  test    - 测试数据库连接"
        exit 1
        ;;
esac
