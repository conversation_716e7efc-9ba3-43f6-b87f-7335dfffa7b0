#!/usr/bin/env python3
"""
测试SQL代理连接的脚本
"""

import socket
import sys
import time

def test_port(host, port, timeout=5):
    """测试端口连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"连接测试出错: {e}")
        return False

def main():
    print("🔍 测试SQL代理连接...")
    print("=" * 40)
    
    # 测试本地代理端口
    print("1. 测试本地代理端口 (127.0.0.1:3307)...")
    if test_port('127.0.0.1', 3307):
        print("   ✅ 本地代理端口可用")
    else:
        print("   ❌ 本地代理端口不可用")
        print("   请确保代理服务器正在运行")
        return
    
    # 测试原始数据库连接
    print("2. 测试原始数据库连接...")
    if test_port('rm-2ze3im04rxy44o880.mysql.rds.aliyuncs.com', 3306):
        print("   ✅ 可以直接连接到数据库")
    else:
        print("   ❌ 无法直接连接到数据库")
    
    print("\n🎉 连接测试完成！")
    print("\n📋 连接信息:")
    print("   主机: 127.0.0.1 (或 localhost)")
    print("   端口: 3307")
    print("   用户名: automatic_targeting_test")
    print("   密码: kru6YVF7bze*wtj8aux")
    print("\n💡 MySQL连接命令:")
    print("   mysql -h 127.0.0.1 -P 3307 -u automatic_targeting_test -p")

if __name__ == "__main__":
    main()
