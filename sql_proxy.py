#!/usr/bin/env python3
"""
SQL端口转发代理服务器
将本地端口转发到阿里云RDS MySQL数据库
"""

import socket
import threading
import sys
import signal
import time
import argparse
from datetime import datetime

# 数据库配置
DB_HOST = "rm-2ze3im04rxy44o880.mysql.rds.aliyuncs.com"
DB_PORT = 3306
DB_USER = "automatic_targeting_test"
DB_PASSWORD = "kru6YVF7bze*wtj8aux"

# 默认本地端口
DEFAULT_LOCAL_PORT = 3307

class SQLProxy:
    def __init__(self, local_port=DEFAULT_LOCAL_PORT, remote_host=DB_HOST, remote_port=DB_PORT):
        self.local_port = local_port
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.server_socket = None
        self.running = False
        self.connections = []
        
    def log(self, message):
        """打印带时间戳的日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        try:
            self.log(f"新连接来自 {client_address[0]}:{client_address[1]}")
            
            # 连接到远程数据库
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.settimeout(10)
            remote_socket.connect((self.remote_host, self.remote_port))
            
            self.log(f"已连接到数据库 {self.remote_host}:{self.remote_port}")
            
            # 创建双向数据转发线程
            client_to_remote = threading.Thread(
                target=self.forward_data,
                args=(client_socket, remote_socket, f"{client_address[0]}:{client_address[1]} -> DB")
            )
            remote_to_client = threading.Thread(
                target=self.forward_data,
                args=(remote_socket, client_socket, f"DB -> {client_address[0]}:{client_address[1]}")
            )
            
            client_to_remote.daemon = True
            remote_to_client.daemon = True
            
            client_to_remote.start()
            remote_to_client.start()
            
            # 等待任一方向的连接断开
            client_to_remote.join()
            remote_to_client.join()
            
        except Exception as e:
            self.log(f"处理客户端连接时出错: {e}")
        finally:
            try:
                client_socket.close()
                remote_socket.close()
            except:
                pass
            self.log(f"连接 {client_address[0]}:{client_address[1]} 已关闭")
            
    def forward_data(self, source, destination, direction):
        """转发数据"""
        try:
            while self.running:
                data = source.recv(4096)
                if not data:
                    break
                destination.send(data)
        except Exception as e:
            if self.running:  # 只在服务运行时记录错误
                self.log(f"数据转发错误 ({direction}): {e}")
        finally:
            try:
                source.close()
                destination.close()
            except:
                pass
                
    def start(self):
        """启动代理服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.local_port))
            self.server_socket.listen(5)
            
            self.running = True
            
            print("=" * 50)
            print("🚀 SQL端口转发代理服务器已启动")
            print("=" * 50)
            print(f"本地监听端口: {self.local_port}")
            print(f"目标数据库: {self.remote_host}:{self.remote_port}")
            print(f"数据库用户: {DB_USER}")
            print("")
            print("连接方式:")
            print(f"  主机: localhost 或 127.0.0.1")
            print(f"  端口: {self.local_port}")
            print(f"  用户名: {DB_USER}")
            print(f"  密码: {DB_PASSWORD}")
            print("")
            print("MySQL连接命令示例:")
            print(f"  mysql -h 127.0.0.1 -P {self.local_port} -u {DB_USER} -p")
            print("")
            print("按 Ctrl+C 停止服务")
            print("=" * 50)
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    
                    # 为每个客户端连接创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                    self.connections.append(client_thread)
                    
                except socket.error as e:
                    if self.running:
                        self.log(f"接受连接时出错: {e}")
                        
        except Exception as e:
            self.log(f"启动服务器时出错: {e}")
            return False
            
        return True
        
    def stop(self):
        """停止代理服务器"""
        self.log("正在停止代理服务器...")
        self.running = False
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
                
        self.log("代理服务器已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在关闭服务器...")
    if 'proxy' in globals():
        proxy.stop()
    sys.exit(0)

def test_connection(host, port):
    """测试连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def main():
    parser = argparse.ArgumentParser(description='SQL端口转发代理服务器')
    parser.add_argument('-p', '--port', type=int, default=DEFAULT_LOCAL_PORT,
                       help=f'本地监听端口 (默认: {DEFAULT_LOCAL_PORT})')
    parser.add_argument('-t', '--test', action='store_true',
                       help='测试数据库连接')
    parser.add_argument('--host', default=DB_HOST,
                       help=f'远程数据库主机 (默认: {DB_HOST})')
    parser.add_argument('--remote-port', type=int, default=DB_PORT,
                       help=f'远程数据库端口 (默认: {DB_PORT})')
    
    args = parser.parse_args()
    
    if args.test:
        print("测试数据库连接...")
        if test_connection(args.host, args.remote_port):
            print(f"✅ 可以连接到 {args.host}:{args.remote_port}")
        else:
            print(f"❌ 无法连接到 {args.host}:{args.remote_port}")
        return
    
    # 检查端口是否被占用
    if test_connection('127.0.0.1', args.port):
        print(f"❌ 端口 {args.port} 已被占用，请选择其他端口")
        return
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动代理
    global proxy
    proxy = SQLProxy(args.port, args.host, args.remote_port)
    
    try:
        proxy.start()
    except KeyboardInterrupt:
        proxy.stop()

if __name__ == "__main__":
    main()
