# SQL端口转发工具

这个工具可以将本地端口转发到阿里云RDS MySQL数据库，让其他机器可以通过连接到这台服务器来访问数据库。

## 数据库信息

- **主机**: rm-2ze3im04rxy44o880.mysql.rds.aliyuncs.com
- **端口**: 3306
- **用户名**: automatic_targeting_test
- **密码**: kru6YVF7bze*wtj8aux

## 使用方法

### 方法1: 使用Python代理 (推荐)

Python版本更稳定，支持多并发连接：

```bash
# 启动代理服务器 (默认本地端口3307)
python3 sql_proxy.py

# 使用自定义端口
python3 sql_proxy.py -p 3308

# 测试数据库连接
python3 sql_proxy.py --test
```

### 方法2: 使用Shell脚本

```bash
# 给脚本执行权限
chmod +x sql-port-forward.sh

# 启动端口转发
./sql-port-forward.sh start

# 停止端口转发
./sql-port-forward.sh stop

# 查看状态
./sql-port-forward.sh status

# 测试连接
./sql-port-forward.sh test
```

## 连接数据库

启动端口转发后，可以通过以下方式连接：

### MySQL命令行
```bash
mysql -h 127.0.0.1 -P 3307 -u automatic_targeting_test -p
# 密码: kru6YVF7bze*wtj8aux
```

### 其他应用程序
- **主机**: localhost 或 127.0.0.1
- **端口**: 3307 (或您指定的端口)
- **用户名**: automatic_targeting_test
- **密码**: kru6YVF7bze*wtj8aux

## 从其他机器连接

如果您想让其他机器通过这台服务器访问数据库：

1. 确保防火墙允许访问转发端口 (默认3307)
2. 其他机器连接时使用这台服务器的IP地址

例如，如果这台服务器的IP是 `*************`：

```bash
mysql -h ************* -P 3307 -u automatic_targeting_test -p
```

## 安全注意事项

1. **防火墙设置**: 确保只有信任的机器可以访问转发端口
2. **网络安全**: 在生产环境中，建议使用VPN或其他安全连接方式
3. **监控**: 定期检查连接日志，确保没有异常访问

## 故障排除

### 端口被占用
```bash
# 查看端口占用情况
netstat -tuln | grep 3307

# 或使用lsof
lsof -i :3307
```

### 无法连接到数据库
```bash
# 测试直连数据库
python3 sql_proxy.py --test

# 检查网络连接
ping rm-2ze3im04rxy44o880.mysql.rds.aliyuncs.com
```

### 查看日志
Python版本会显示详细的连接日志，包括：
- 新连接建立
- 数据转发状态
- 连接断开信息

## 停止服务

- Python版本: 按 `Ctrl+C`
- Shell版本: `./sql-port-forward.sh stop`

## 自动启动 (可选)

如果需要开机自动启动，可以创建systemd服务：

```bash
# 创建服务文件
sudo nano /etc/systemd/system/sql-proxy.service
```

服务文件内容：
```ini
[Unit]
Description=SQL Port Forward Proxy
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/script
ExecStart=/usr/bin/python3 /path/to/your/script/sql_proxy.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

然后启用服务：
```bash
sudo systemctl enable sql-proxy.service
sudo systemctl start sql-proxy.service
```
